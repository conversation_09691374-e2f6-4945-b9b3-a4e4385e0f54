# Job Application Automation System - Environment Variables

# Application Settings
APP_NAME="Job Application Automation"
DEBUG=false
LOG_LEVEL=INFO

# Security Settings
MASTER_PASSWORD=""
SESSION_TIMEOUT=3600

# Browser Automation Settings
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
AUTOMATION_DELAY=2000

# Job Portal Credentials (Optional - can be set in UI)
# We Work Remotely
WWR_EMAIL=""
WWR_PASSWORD=""

# FlexJobs
FLEXJOBS_EMAIL=""
FLEXJOBS_PASSWORD=""

# Remote.co
REMOTE_CO_EMAIL=""
REMOTE_CO_PASSWORD=""

# Jobspresso
JOBSPRESSO_EMAIL=""
JOBSPRESSO_PASSWORD=""

# Application Limits
DAILY_APPLICATION_LIMIT=10
MAX_APPLICATIONS_PER_PORTAL=5

# Data Storage
DATA_RETENTION_DAYS=90
AUTO_BACKUP=true

# Notification Settings
ENABLE_NOTIFICATIONS=true
EMAIL_NOTIFICATIONS=false
NOTIFICATION_EMAIL=""
