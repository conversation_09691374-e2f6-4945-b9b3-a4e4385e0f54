"""
Modern Job Application Automation System
A beautiful, privacy-focused job application automation platform.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
from streamlit_option_menu import option_menu
import extra_streamlit_components as stx
from datetime import datetime
import json
from pathlib import Path

# Import our modules
from models import (
    UserProfile, JobApplication, ApplicationSettings, SystemStatus,
    JobPortalName, ApplicationStatus, ExperienceLevel
)
from utils.config import config_manager
from utils.logger import get_logger

# Configure page
st.set_page_config(
    page_title="JobFlow - Smart Job Application Automation",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for modern styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }

    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }

    .step-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
        transition: all 0.3s ease;
    }

    .step-card:hover {
        border-color: #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    .success-message {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }

    .stButton > button {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }

    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }
</style>
""", unsafe_allow_html=True)

# Initialize logger
logger = get_logger(__name__)

# Initialize session state
def init_session_state():
    """Initialize session state variables."""
    if 'onboarding_complete' not in st.session_state:
        st.session_state.onboarding_complete = False
    if 'current_step' not in st.session_state:
        st.session_state.current_step = 0
    if 'user_profile' not in st.session_state:
        st.session_state.user_profile = None
    if 'applications' not in st.session_state:
        st.session_state.applications = []
    if 'settings' not in st.session_state:
        st.session_state.settings = ApplicationSettings()
    if 'system_status' not in st.session_state:
        st.session_state.system_status = SystemStatus()


def load_user_data():
    """Load user data from local storage."""
    try:
        data_dir = config_manager.get_data_dir()

        # Check if user has completed onboarding
        onboarding_file = data_dir / "onboarding_complete.json"
        if onboarding_file.exists():
            st.session_state.onboarding_complete = True

        # Load user profile
        profile_file = data_dir / "user_profile.json"
        if profile_file.exists():
            with open(profile_file, 'r') as f:
                profile_data = json.load(f)
                st.session_state.user_profile = UserProfile(**profile_data)

        # Load applications
        apps_file = data_dir / "applications.json"
        if apps_file.exists():
            with open(apps_file, 'r') as f:
                apps_data = json.load(f)
                st.session_state.applications = [JobApplication(**app) for app in apps_data]

        # Load settings
        settings_file = data_dir / "settings.json"
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                settings_data = json.load(f)
                st.session_state.settings = ApplicationSettings(**settings_data)

    except Exception as e:
        logger.error(f"Error loading user data: {e}")


def save_user_data():
    """Save user data to local storage."""
    try:
        data_dir = config_manager.get_data_dir()

        # Mark onboarding as complete
        if st.session_state.onboarding_complete:
            onboarding_file = data_dir / "onboarding_complete.json"
            with open(onboarding_file, 'w') as f:
                json.dump({"completed": True, "date": datetime.now().isoformat()}, f)

        # Save user profile
        if st.session_state.user_profile:
            profile_file = data_dir / "user_profile.json"
            with open(profile_file, 'w') as f:
                json.dump(st.session_state.user_profile.dict(), f, indent=2, default=str)

        # Save applications
        apps_file = data_dir / "applications.json"
        with open(apps_file, 'w') as f:
            apps_data = [app.dict() for app in st.session_state.applications]
            json.dump(apps_data, f, indent=2, default=str)

        # Save settings
        settings_file = data_dir / "settings.json"
        with open(settings_file, 'w') as f:
            json.dump(st.session_state.settings.dict(), f, indent=2, default=str)

    except Exception as e:
        logger.error(f"Error saving user data: {e}")
        st.error(f"Error saving user data: {e}")


def render_welcome_screen():
    """Render the welcome/landing screen."""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 Welcome to JobFlow</h1>
        <h3>Smart Job Application Automation Platform</h3>
        <p>Streamline your job search with privacy-focused automation</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🔒 Privacy First</h4>
            <p>All your data stays local. No cloud storage, complete control over your information.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>🤖 Smart Automation</h4>
            <p>Intelligent job matching and application automation across multiple platforms.</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-card">
            <h4>📊 Track Progress</h4>
            <p>Monitor your applications, success rates, and optimize your job search strategy.</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("---")

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("� Get Started", use_container_width=True, type="primary"):
            st.session_state.current_step = 1
            st.rerun()

        st.markdown("### ✨ What you'll get:")
        st.markdown("""
        - **Multi-platform job search** across We Work Remotely, FlexJobs, Remote.co, and more
        - **Automated application submission** with custom cover letters
        - **Real-time job alerts** and duplicate detection
        - **Application tracking** and success analytics
        - **Privacy controls** and data retention management
        """)


def render_profile_setup():
    """Render the profile setup page."""
    st.header("👤 Profile Setup")
    st.write("Configure your profile and preferences for job applications.")
    
    # Create form for profile
    with st.form("profile_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Personal Information")
            name = st.text_input("Full Name", value=st.session_state.user_profile.name if st.session_state.user_profile else "")
            email = st.text_input("Email", value=st.session_state.user_profile.email if st.session_state.user_profile else "")
            phone = st.text_input("Phone (optional)", value=st.session_state.user_profile.phone if st.session_state.user_profile else "")
        
        with col2:
            st.subheader("Experience")
            experience_level = st.selectbox(
                "Experience Level",
                options=[level.value for level in ExperienceLevel],
                index=list(ExperienceLevel).index(st.session_state.user_profile.experience_level) if st.session_state.user_profile else 2
            )
            years_experience = st.number_input(
                "Years of Experience",
                min_value=0, max_value=50,
                value=st.session_state.user_profile.years_of_experience if st.session_state.user_profile else 0
            )
        
        st.subheader("Skills")
        skills_input = st.text_area(
            "Skills (one per line)",
            value="\n".join(st.session_state.user_profile.skills) if st.session_state.user_profile else "",
            help="Enter your skills, one per line"
        )
        
        col3, col4 = st.columns(2)
        with col3:
            st.subheader("Job Preferences")
            job_types_input = st.text_area(
                "Preferred Job Types (one per line)",
                value="\n".join(st.session_state.user_profile.preferred_job_types) if st.session_state.user_profile else "",
                help="e.g., Software Engineer, Data Scientist"
            )
        
        with col4:
            st.subheader("Location Preferences")
            locations_input = st.text_area(
                "Preferred Locations (one per line)",
                value="\n".join(st.session_state.user_profile.preferred_locations) if st.session_state.user_profile else "",
                help="e.g., Remote, New York, San Francisco"
            )
        
        col5, col6 = st.columns(2)
        with col5:
            salary_min = st.number_input(
                "Minimum Salary (optional)",
                min_value=0, step=1000,
                value=st.session_state.user_profile.preferred_salary_min if st.session_state.user_profile and st.session_state.user_profile.preferred_salary_min else 0
            )
        with col6:
            salary_max = st.number_input(
                "Maximum Salary (optional)",
                min_value=0, step=1000,
                value=st.session_state.user_profile.preferred_salary_max if st.session_state.user_profile and st.session_state.user_profile.preferred_salary_max else 0
            )
        
        st.subheader("Resume & Cover Letter")
        resume_file = st.file_uploader("Upload Resume", type=['pdf', 'doc', 'docx'])
        cover_letter_template = st.text_area(
            "Cover Letter Template",
            value=st.session_state.user_profile.cover_letter_template if st.session_state.user_profile else "",
            help="Use {company_name} and {position_title} as placeholders",
            height=150
        )
        
        # Submit button
        if st.form_submit_button("💾 Save Profile", use_container_width=True):
            try:
                # Process inputs
                skills = [skill.strip() for skill in skills_input.split('\n') if skill.strip()]
                job_types = [jt.strip() for jt in job_types_input.split('\n') if jt.strip()]
                locations = [loc.strip() for loc in locations_input.split('\n') if loc.strip()]
                
                # Handle resume upload
                resume_path = None
                if resume_file:
                    resume_dir = config_manager.get_data_dir() / "resumes"
                    resume_dir.mkdir(exist_ok=True)
                    resume_path = resume_dir / resume_file.name
                    with open(resume_path, 'wb') as f:
                        f.write(resume_file.getbuffer())
                
                # Create profile
                profile_data = {
                    "name": name,
                    "email": email,
                    "phone": phone if phone else None,
                    "skills": skills,
                    "experience_level": experience_level,
                    "years_of_experience": years_experience,
                    "preferred_job_types": job_types,
                    "preferred_locations": locations,
                    "preferred_salary_min": salary_min if salary_min > 0 else None,
                    "preferred_salary_max": salary_max if salary_max > 0 else None,
                    "resume_path": str(resume_path) if resume_path else None,
                    "cover_letter_template": cover_letter_template
                }
                
                st.session_state.user_profile = UserProfile(**profile_data)
                save_user_data()
                st.success("✅ Profile saved successfully!")
                
            except Exception as e:
                st.error(f"Error saving profile: {e}")


def render_job_search():
    """Render the job search page."""
    st.header("🔍 Job Search")
    st.write("Search and apply to jobs across multiple portals.")
    
    if not st.session_state.user_profile:
        st.warning("⚠️ Please complete your profile setup first.")
        return
    
    # Search form
    with st.form("search_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            keywords = st.text_input(
                "Keywords",
                value="python developer",
                help="Enter job keywords separated by commas"
            )
            location = st.text_input("Location", value="Remote")
        
        with col2:
            portals = st.multiselect(
                "Job Portals",
                options=[portal.value for portal in JobPortalName],
                default=[JobPortalName.WE_WORK_REMOTELY.value],
                help="Select portals to search"
            )
            max_results = st.number_input("Max Results per Portal", min_value=1, max_value=100, value=20)
        
        search_clicked = st.form_submit_button("🔍 Search Jobs", use_container_width=True)
    
    # Search results
    if search_clicked:
        if not keywords.strip():
            st.error("Please enter search keywords.")
            return
        
        with st.spinner("Searching for jobs..."):
            try:
                # Convert portal names back to enum
                selected_portals = [JobPortalName(portal) for portal in portals]
                keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                
                # Search jobs using API
                results = asyncio.run(api_manager.search_all_portals(
                    keywords=keyword_list,
                    location=location,
                    enabled_portals=selected_portals
                ))
                
                # Display results
                total_jobs = sum(len(jobs) for jobs in results.values())
                st.success(f"Found {total_jobs} jobs across {len(results)} portals!")
                
                for portal_name, jobs in results.items():
                    if jobs:
                        st.subheader(f"{portal_name.value} ({len(jobs)} jobs)")
                        
                        for job in jobs[:max_results]:
                            with st.expander(f"{job['title']} - {job['company']}"):
                                col1, col2 = st.columns([3, 1])
                                
                                with col1:
                                    st.write(f"**Company:** {job['company']}")
                                    st.write(f"**Location:** {job.get('location', 'Not specified')}")
                                    st.write(f"**Posted:** {job.get('posted_date', 'Not specified')}")
                                    if job.get('description'):
                                        st.write("**Description:**")
                                        st.write(job['description'][:300] + "..." if len(job['description']) > 300 else job['description'])
                                
                                with col2:
                                    if st.button(f"Apply", key=f"apply_{job['id']}"):
                                        st.info("Application feature coming soon!")
                                    
                                    if st.button(f"View Job", key=f"view_{job['id']}"):
                                        st.write(f"[Open Job Posting]({job['url']})")
                
            except Exception as e:
                st.error(f"Error searching jobs: {e}")
                logger.error(f"Job search error: {e}")


def main():
    """Main application function."""
    # Load data on startup
    if 'data_loaded' not in st.session_state:
        load_user_data()
        st.session_state.data_loaded = True
    
    # Render sidebar and get current page
    current_page = render_sidebar()
    
    # Render main content based on selected page
    if current_page == "Profile Setup":
        render_profile_setup()
    elif current_page == "Job Search":
        render_job_search()
    elif current_page == "Applications":
        st.header("📋 Applications")
        st.info("Applications tracking page coming soon!")
    elif current_page == "Settings":
        st.header("⚙️ Settings")
        st.info("Settings page coming soon!")


if __name__ == "__main__":
    main()
