# Pydantic

> Data validation using Python type hints

Pydantic is the most widely used data validation library for Python.
Fast and extensible, Pydantic plays nicely with your linters/IDE/brain.
Define how data should be in pure, canonical Python 3.9+; validate it with Pydantic.

## Concepts documentation

- [Alias](https://docs.pydantic.dev/latest/concepts/alias/index.md)
- [Configuration](https://docs.pydantic.dev/latest/concepts/config/index.md)
- [Conversion Table](https://docs.pydantic.dev/latest/concepts/conversion_table/index.md)
- [Dataclasses](https://docs.pydantic.dev/latest/concepts/dataclasses/index.md)
- [Experimental](https://docs.pydantic.dev/latest/concepts/experimental/index.md)
- [Fields](https://docs.pydantic.dev/latest/concepts/fields/index.md)
- [Forward Annotations](https://docs.pydantic.dev/latest/concepts/forward_annotations/index.md)
- [JSON](https://docs.pydantic.dev/latest/concepts/json/index.md)
- [JSON Schema](https://docs.pydantic.dev/latest/concepts/json_schema/index.md)
- [Models](https://docs.pydantic.dev/latest/concepts/models/index.md)
- [Performance](https://docs.pydantic.dev/latest/concepts/performance/index.md)
- [Settings Management](https://docs.pydantic.dev/latest/concepts/pydantic_settings/index.md)
- [Serialization](https://docs.pydantic.dev/latest/concepts/serialization/index.md)
- [Strict Mode](https://docs.pydantic.dev/latest/concepts/strict_mode/index.md)
- [Type Adapter](https://docs.pydantic.dev/latest/concepts/type_adapter/index.md)
- [Types](https://docs.pydantic.dev/latest/concepts/types/index.md)
- [Unions](https://docs.pydantic.dev/latest/concepts/unions/index.md)
- [Validation Decorator](https://docs.pydantic.dev/latest/concepts/validation_decorator/index.md)
- [Validators](https://docs.pydantic.dev/latest/concepts/validators/index.md)

## API documentation

- [Aliases](https://docs.pydantic.dev/latest/api/aliases/index.md)
- [Annotated Handlers](https://docs.pydantic.dev/latest/api/annotated_handlers/index.md)
- [BaseModel](https://docs.pydantic.dev/latest/api/base_model/index.md)
- [Configuration](https://docs.pydantic.dev/latest/api/config/index.md)
- [Pydantic Dataclasses](https://docs.pydantic.dev/latest/api/dataclasses/index.md)
- [Errors](https://docs.pydantic.dev/latest/api/errors/index.md)
- [Experimental](https://docs.pydantic.dev/latest/api/experimental/index.md)
- [Fields](https://docs.pydantic.dev/latest/api/fields/index.md)
- [Functional Serializers](https://docs.pydantic.dev/latest/api/functional_serializers/index.md)
- [Functional Validators](https://docs.pydantic.dev/latest/api/functional_validators/index.md)
- [JSON Schema](https://docs.pydantic.dev/latest/api/json_schema/index.md)
- [Network Types](https://docs.pydantic.dev/latest/api/networks/index.md)
- [pydantic_core](https://docs.pydantic.dev/latest/api/pydantic_core/index.md)
- [pydantic_core.core_schema](https://docs.pydantic.dev/latest/api/pydantic_core_schema/index.md)
- [Color](https://docs.pydantic.dev/latest/api/pydantic_extra_types_color/index.md)
- [Coordinate](https://docs.pydantic.dev/latest/api/pydantic_extra_types_coordinate/index.md)
- [Country](https://docs.pydantic.dev/latest/api/pydantic_extra_types_country/index.md)
- [Currency](https://docs.pydantic.dev/latest/api/pydantic_extra_types_currency_code/index.md)
- [ISBN](https://docs.pydantic.dev/latest/api/pydantic_extra_types_isbn/index.md)
- [Language](https://docs.pydantic.dev/latest/api/pydantic_extra_types_language_code/index.md)
- [Mac Address](https://docs.pydantic.dev/latest/api/pydantic_extra_types_mac_address/index.md)
- [Payment](https://docs.pydantic.dev/latest/api/pydantic_extra_types_payment/index.md)
- [Pendulum](https://docs.pydantic.dev/latest/api/pydantic_extra_types_pendulum_dt/index.md)
- [Phone Numbers](https://docs.pydantic.dev/latest/api/pydantic_extra_types_phone_numbers/index.md)
- [Routing Numbers](https://docs.pydantic.dev/latest/api/pydantic_extra_types_routing_numbers/index.md)
- [Script Code](https://docs.pydantic.dev/latest/api/pydantic_extra_types_script_code/index.md)
- [Semantic Version](https://docs.pydantic.dev/latest/api/pydantic_extra_types_semantic_version/index.md)
- [Timezone Name](https://docs.pydantic.dev/latest/api/pydantic_extra_types_timezone_name/index.md)
- [ULID](https://docs.pydantic.dev/latest/api/pydantic_extra_types_ulid/index.md)
- [Pydantic Settings](https://docs.pydantic.dev/latest/api/pydantic_settings/index.md)
- [RootModel](https://docs.pydantic.dev/latest/api/root_model/index.md)
- [Standard Library Types](https://docs.pydantic.dev/latest/api/standard_library_types/index.md)
- [TypeAdapter](https://docs.pydantic.dev/latest/api/type_adapter/index.md)
- [Pydantic Types](https://docs.pydantic.dev/latest/api/types/index.md)
- [Validate Call](https://docs.pydantic.dev/latest/api/validate_call/index.md)
- [Version Information](https://docs.pydantic.dev/latest/api/version/index.md)

## Internals

- [Architecture](https://docs.pydantic.dev/latest/internals/architecture/index.md)
- [Resolving Annotations](https://docs.pydantic.dev/latest/internals/resolving_annotations/index.md)

## Optional

- [Error Handling](https://docs.pydantic.dev/latest/errors/errors/index.md)
- [Usage Errors](https://docs.pydantic.dev/latest/errors/usage_errors/index.md)
- [Validation Errors](https://docs.pydantic.dev/latest/errors/validation_errors/index.md)
- [Custom Validators](https://docs.pydantic.dev/latest/examples/custom_validators/index.md)
- [Validating File Data](https://docs.pydantic.dev/latest/examples/files/index.md)
- [Databases](https://docs.pydantic.dev/latest/examples/orms/index.md)
- [Queues](https://docs.pydantic.dev/latest/examples/queues/index.md)
- [Web and API Requests](https://docs.pydantic.dev/latest/examples/requests/index.md)
- [AWS Lambda](https://docs.pydantic.dev/latest/integrations/aws_lambda/index.md)
- [datamodel-code-generator](https://docs.pydantic.dev/latest/integrations/datamodel_code_generator/index.md)
- [devtools](https://docs.pydantic.dev/latest/integrations/devtools/index.md)
- [Documentation](https://docs.pydantic.dev/latest/integrations/documentation/index.md)
- [Hypothesis](https://docs.pydantic.dev/latest/integrations/hypothesis/index.md)
- [Linting](https://docs.pydantic.dev/latest/integrations/linting/index.md)
- [LLMs](https://docs.pydantic.dev/latest/integrations/llms/index.md)
- [Pydantic Logfire](https://docs.pydantic.dev/latest/integrations/logfire/index.md)
- [Mypy](https://docs.pydantic.dev/latest/integrations/mypy/index.md)
- [PyCharm](https://docs.pydantic.dev/latest/integrations/pycharm/index.md)
- [Rich](https://docs.pydantic.dev/latest/integrations/rich/index.md)
- [Visual Studio Code](https://docs.pydantic.dev/latest/integrations/visual_studio_code/index.md)

