"""
Pydantic models for the Job Application Automation System.
Provides data validation and serialization for user profiles, job applications, and settings.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr, Field, validator


class ExperienceLevel(str, Enum):
    """Experience level options."""
    ENTRY = "entry"
    JUNIOR = "junior"
    MID = "mid"
    SENIOR = "senior"
    LEAD = "lead"
    EXECUTIVE = "executive"


class ApplicationStatus(str, Enum):
    """Application status options."""
    PENDING = "pending"
    APPLIED = "applied"
    INTERVIEW = "interview"
    REJECTED = "rejected"
    OFFER = "offer"
    ACCEPTED = "accepted"
    WITHDRAWN = "withdrawn"


class JobPortalName(str, Enum):
    """Supported job portals."""
    WE_WORK_REMOTELY = "we_work_remotely"
    FLEXJOBS = "flexjobs"
    REMOTE_CO = "remote_co"
    JOBSPRESSO = "jobspresso"


class UserProfile(BaseModel):
    """User profile model with validation."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Full name")
    email: EmailStr = Field(..., description="Email address")
    phone: Optional[str] = Field(None, pattern=r"^\+?[\d\s\-\(\)]+$", description="Phone number")
    
    # Skills and experience
    skills: List[str] = Field(default_factory=list, description="List of skills")
    experience_level: ExperienceLevel = Field(default=ExperienceLevel.MID, description="Experience level")
    years_of_experience: int = Field(default=0, ge=0, le=50, description="Years of experience")
    
    # Job preferences
    preferred_job_types: List[str] = Field(default_factory=list, description="Preferred job types")
    preferred_locations: List[str] = Field(default_factory=list, description="Preferred locations")
    preferred_salary_min: Optional[int] = Field(None, ge=0, description="Minimum salary preference")
    preferred_salary_max: Optional[int] = Field(None, ge=0, description="Maximum salary preference")
    
    # Resume and cover letter
    resume_path: Optional[str] = Field(None, description="Path to resume file")
    cover_letter_template: Optional[str] = Field(None, description="Cover letter template")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    @validator('preferred_salary_max')
    def validate_salary_range(cls, v, values):
        """Ensure max salary is greater than min salary."""
        if v is not None and 'preferred_salary_min' in values and values['preferred_salary_min'] is not None:
            if v < values['preferred_salary_min']:
                raise ValueError('Maximum salary must be greater than minimum salary')
        return v


class JobApplication(BaseModel):
    """Job application model."""
    
    id: str = Field(..., description="Unique application ID")
    job_id: str = Field(..., description="Job ID from the portal")
    portal_name: JobPortalName = Field(..., description="Job portal name")
    
    # Job details
    position_title: str = Field(..., min_length=1, description="Job position title")
    company_name: str = Field(..., min_length=1, description="Company name")
    job_url: str = Field(..., description="URL to the job posting")
    
    # Application details
    status: ApplicationStatus = Field(default=ApplicationStatus.PENDING, description="Application status")
    submission_timestamp: Optional[datetime] = Field(None, description="When application was submitted")
    custom_cover_letter: Optional[str] = Field(None, description="Custom cover letter content")
    
    # Tracking
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional application metadata")


class JobPortal(BaseModel):
    """Job portal configuration model."""
    
    name: JobPortalName = Field(..., description="Portal name")
    base_url: str = Field(..., description="Base URL of the portal")
    login_url: Optional[str] = Field(None, description="Login URL")
    
    # Authentication
    requires_auth: bool = Field(default=True, description="Whether portal requires authentication")
    auth_method: str = Field(default="email_password", description="Authentication method")
    
    # Capabilities
    supports_resume_upload: bool = Field(default=True, description="Supports resume upload")
    supports_cover_letter: bool = Field(default=True, description="Supports cover letter")
    supports_job_alerts: bool = Field(default=False, description="Supports job alerts")
    
    # Rate limiting
    max_applications_per_day: int = Field(default=5, ge=1, description="Max applications per day")
    delay_between_applications: int = Field(default=60, ge=10, description="Delay between applications in seconds")
    
    # Status
    is_active: bool = Field(default=True, description="Whether portal is active")
    last_used: Optional[datetime] = Field(None, description="Last time portal was used")


class ApplicationSettings(BaseModel):
    """Application automation settings."""
    
    # Daily limits
    daily_application_limit: int = Field(default=10, ge=1, le=50, description="Daily application limit")
    applications_per_portal_limit: int = Field(default=5, ge=1, le=20, description="Applications per portal limit")
    
    # Timing preferences
    preferred_start_time: str = Field(default="09:00", pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Preferred start time (HH:MM)")
    preferred_end_time: str = Field(default="17:00", pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Preferred end time (HH:MM)")
    
    # Portal preferences
    enabled_portals: List[JobPortalName] = Field(default_factory=lambda: list(JobPortalName), description="Enabled job portals")
    portal_priorities: Dict[JobPortalName, int] = Field(default_factory=dict, description="Portal priority rankings")
    
    # Automation behavior
    auto_apply: bool = Field(default=False, description="Enable automatic application")
    require_manual_review: bool = Field(default=True, description="Require manual review before applying")
    skip_duplicate_jobs: bool = Field(default=True, description="Skip duplicate job postings")
    
    # Privacy and security
    data_retention_days: int = Field(default=90, ge=1, le=365, description="Data retention period in days")
    auto_backup: bool = Field(default=True, description="Enable automatic backups")
    encrypt_sensitive_data: bool = Field(default=True, description="Encrypt sensitive data")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class SystemStatus(BaseModel):
    """System status and health model."""
    
    is_running: bool = Field(default=False, description="Whether automation is running")
    current_portal: Optional[JobPortalName] = Field(None, description="Currently active portal")
    applications_today: int = Field(default=0, description="Applications submitted today")
    last_application_time: Optional[datetime] = Field(None, description="Last application submission time")
    
    # Health metrics
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="Application success rate")
    error_count: int = Field(default=0, ge=0, description="Number of errors today")
    last_error: Optional[str] = Field(None, description="Last error message")
    
    updated_at: datetime = Field(default_factory=datetime.now)
