"""
Browser automation module for job application portals.
Handles web scraping, form filling, and application submission using <PERSON><PERSON>.
"""

import asyncio
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, TimeoutError

from ..models import JobPortalName, JobApplication, ApplicationStatus
from ..utils.config import config_manager
from ..utils.logger import get_logger, log_automation_event

logger = get_logger(__name__)


class BrowserAutomation:
    """Base class for browser automation."""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.screenshots_dir = config_manager.get_screenshots_dir()
        
    async def start_browser(self, headless: bool = None) -> None:
        """Start the browser instance."""
        if headless is None:
            headless = config_manager.settings.browser_headless
            
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            
            self.page = await self.context.new_page()
            
            # Set default timeout
            self.page.set_default_timeout(config_manager.settings.browser_timeout)
            
            logger.info("Browser started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            await self.cleanup()
            raise
    
    async def cleanup(self) -> None:
        """Clean up browser resources."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            logger.info("Browser cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during browser cleanup: {e}")
    
    async def take_screenshot(self, name: str) -> str:
        """Take a screenshot and save it."""
        if not self.page:
            raise RuntimeError("Browser not started")
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{name}_{timestamp}.png"
        filepath = self.screenshots_dir / filename
        
        await self.page.screenshot(path=str(filepath), full_page=True)
        logger.info(f"Screenshot saved: {filepath}")
        
        return str(filepath)
    
    async def wait_for_element(self, selector: str, timeout: int = 10000) -> bool:
        """Wait for an element to appear."""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout)
            return True
        except TimeoutError:
            logger.warning(f"Element not found: {selector}")
            return False
    
    async def safe_click(self, selector: str, timeout: int = 10000) -> bool:
        """Safely click an element."""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout)
            await self.page.click(selector)
            await self.page.wait_for_timeout(1000)  # Wait for any animations
            return True
        except Exception as e:
            logger.error(f"Failed to click element {selector}: {e}")
            return False
    
    async def safe_fill(self, selector: str, value: str, timeout: int = 10000) -> bool:
        """Safely fill an input field."""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout)
            await self.page.fill(selector, value)
            await self.page.wait_for_timeout(500)
            return True
        except Exception as e:
            logger.error(f"Failed to fill element {selector}: {e}")
            return False
    
    async def upload_file(self, selector: str, file_path: str) -> bool:
        """Upload a file to a file input."""
        try:
            if not Path(file_path).exists():
                logger.error(f"File not found: {file_path}")
                return False
                
            await self.page.wait_for_selector(selector)
            await self.page.set_input_files(selector, file_path)
            await self.page.wait_for_timeout(2000)  # Wait for upload
            return True
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            return False
    
    async def check_for_captcha(self) -> bool:
        """Check if there's a CAPTCHA on the page."""
        captcha_selectors = [
            '[class*="captcha"]',
            '[id*="captcha"]',
            'iframe[src*="recaptcha"]',
            '.g-recaptcha',
            '.h-captcha'
        ]
        
        for selector in captcha_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    logger.warning("CAPTCHA detected on page")
                    return True
            except:
                continue
        
        return False
    
    async def handle_popup(self) -> None:
        """Handle common popups and modals."""
        popup_selectors = [
            '[class*="modal"] [class*="close"]',
            '[class*="popup"] [class*="close"]',
            'button[aria-label*="close"]',
            'button[aria-label*="dismiss"]'
        ]
        
        for selector in popup_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    await element.click()
                    await self.page.wait_for_timeout(1000)
                    logger.info(f"Closed popup using selector: {selector}")
                    break
            except:
                continue


class JobPortalAutomation(BrowserAutomation):
    """Specialized automation for job portals."""
    
    def __init__(self, portal_name: JobPortalName):
        super().__init__()
        self.portal_name = portal_name
        self.is_logged_in = False
    
    async def login(self, email: str, password: str) -> bool:
        """Login to the job portal."""
        try:
            start_time = time.time()
            
            # Get portal-specific login URL
            login_url = self._get_login_url()
            if not login_url:
                logger.error(f"No login URL configured for {self.portal_name}")
                return False
            
            await self.page.goto(login_url)
            await self.take_screenshot(f"{self.portal_name}_login_page")
            
            # Handle any popups
            await self.handle_popup()
            
            # Portal-specific login logic
            success = await self._perform_login(email, password)
            
            if success:
                self.is_logged_in = True
                await self.take_screenshot(f"{self.portal_name}_logged_in")
                
            duration = time.time() - start_time
            log_automation_event("login", self.portal_name.value, success, duration)
            
            return success
            
        except Exception as e:
            logger.error(f"Login failed for {self.portal_name}: {e}")
            log_automation_event("login", self.portal_name.value, False, error=str(e))
            return False
    
    async def search_jobs(self, keywords: List[str], location: str = "") -> List[Dict[str, Any]]:
        """Search for jobs on the portal."""
        try:
            start_time = time.time()
            
            if not self.is_logged_in:
                logger.error("Must be logged in to search jobs")
                return []
            
            # Portal-specific job search logic
            jobs = await self._perform_job_search(keywords, location)
            
            duration = time.time() - start_time
            log_automation_event("job_search", self.portal_name.value, True, duration)
            
            logger.info(f"Found {len(jobs)} jobs for keywords: {keywords}")
            return jobs
            
        except Exception as e:
            logger.error(f"Job search failed for {self.portal_name}: {e}")
            log_automation_event("job_search", self.portal_name.value, False, error=str(e))
            return []
    
    async def apply_to_job(self, job_data: Dict[str, Any], resume_path: str, 
                          cover_letter: str = "") -> bool:
        """Apply to a specific job."""
        try:
            start_time = time.time()
            
            job_url = job_data.get('url', '')
            if not job_url:
                logger.error("No job URL provided")
                return False
            
            await self.page.goto(job_url)
            await self.take_screenshot(f"{self.portal_name}_job_page")
            
            # Check for CAPTCHA
            if await self.check_for_captcha():
                logger.warning("CAPTCHA detected - manual intervention required")
                return False
            
            # Portal-specific application logic
            success = await self._perform_job_application(job_data, resume_path, cover_letter)
            
            if success:
                await self.take_screenshot(f"{self.portal_name}_application_success")
            
            duration = time.time() - start_time
            log_automation_event("job_application", self.portal_name.value, success, duration)
            
            return success
            
        except Exception as e:
            logger.error(f"Job application failed for {self.portal_name}: {e}")
            log_automation_event("job_application", self.portal_name.value, False, error=str(e))
            return False
    
    def _get_login_url(self) -> Optional[str]:
        """Get the login URL for the portal."""
        login_urls = {
            JobPortalName.WE_WORK_REMOTELY: "https://weworkremotely.com/users/sign_in",
            JobPortalName.FLEXJOBS: "https://www.flexjobs.com/login",
            JobPortalName.REMOTE_CO: "https://remote.co/login",
            JobPortalName.JOBSPRESSO: "https://jobspresso.co/login"
        }
        return login_urls.get(self.portal_name)
    
    async def _perform_login(self, email: str, password: str) -> bool:
        """Perform portal-specific login."""
        # This will be implemented for each portal
        # For now, return a placeholder
        logger.warning(f"Login not implemented for {self.portal_name}")
        return False
    
    async def _perform_job_search(self, keywords: List[str], location: str) -> List[Dict[str, Any]]:
        """Perform portal-specific job search."""
        # This will be implemented for each portal
        logger.warning(f"Job search not implemented for {self.portal_name}")
        return []
    
    async def _perform_job_application(self, job_data: Dict[str, Any], 
                                     resume_path: str, cover_letter: str) -> bool:
        """Perform portal-specific job application."""
        # This will be implemented for each portal
        logger.warning(f"Job application not implemented for {self.portal_name}")
        return False
