"""
API client for job portals that support API access.
Handles REST API interactions and RSS feed parsing.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import httpx
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse

from ..models import JobPortalName, JobApplication
from ..utils.config import config_manager
from ..utils.logger import get_logger, log_automation_event

logger = get_logger(__name__)


class APIClient:
    """Base API client for job portals."""
    
    def __init__(self, portal_name: JobPortalName, base_url: str):
        self.portal_name = portal_name
        self.base_url = base_url
        self.session = httpx.AsyncClient(
            timeout=30.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        self.auth_token: Optional[str] = None
    
    async def close(self) -> None:
        """Close the HTTP session."""
        await self.session.aclose()
    
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """Authenticate with the API."""
        try:
            # Portal-specific authentication logic
            success = await self._perform_authentication(credentials)
            
            log_automation_event("api_auth", self.portal_name.value, success)
            return success
            
        except Exception as e:
            logger.error(f"API authentication failed for {self.portal_name}: {e}")
            log_automation_event("api_auth", self.portal_name.value, False, error=str(e))
            return False
    
    async def search_jobs(self, keywords: List[str], location: str = "", 
                         limit: int = 50) -> List[Dict[str, Any]]:
        """Search for jobs using the API."""
        try:
            # Portal-specific job search logic
            jobs = await self._perform_job_search(keywords, location, limit)
            
            log_automation_event("api_job_search", self.portal_name.value, True)
            logger.info(f"Found {len(jobs)} jobs via API for {self.portal_name}")
            
            return jobs
            
        except Exception as e:
            logger.error(f"API job search failed for {self.portal_name}: {e}")
            log_automation_event("api_job_search", self.portal_name.value, False, error=str(e))
            return []
    
    async def get_job_details(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific job."""
        try:
            # Portal-specific job details logic
            job_details = await self._get_job_details(job_id)
            
            if job_details:
                log_automation_event("api_job_details", self.portal_name.value, True)
            
            return job_details
            
        except Exception as e:
            logger.error(f"Failed to get job details for {job_id}: {e}")
            log_automation_event("api_job_details", self.portal_name.value, False, error=str(e))
            return None
    
    async def _perform_authentication(self, credentials: Dict[str, str]) -> bool:
        """Perform portal-specific authentication."""
        # Override in subclasses
        return False
    
    async def _perform_job_search(self, keywords: List[str], location: str, 
                                limit: int) -> List[Dict[str, Any]]:
        """Perform portal-specific job search."""
        # Override in subclasses
        return []
    
    async def _get_job_details(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get portal-specific job details."""
        # Override in subclasses
        return None


class WeWorkRemotelyClient(APIClient):
    """API client for We Work Remotely (RSS feed based)."""
    
    def __init__(self):
        super().__init__(JobPortalName.WE_WORK_REMOTELY, "https://weworkremotely.com")
        self.rss_url = "https://weworkremotely.com/categories/remote-programming-jobs.rss"
    
    async def _perform_authentication(self, credentials: Dict[str, str]) -> bool:
        """We Work Remotely doesn't require authentication for RSS."""
        return True
    
    async def _perform_job_search(self, keywords: List[str], location: str = "", 
                                limit: int = 50) -> List[Dict[str, Any]]:
        """Search jobs using RSS feed."""
        try:
            response = await self.session.get(self.rss_url)
            response.raise_for_status()
            
            # Parse RSS feed
            root = ET.fromstring(response.content)
            jobs = []
            
            for item in root.findall('.//item')[:limit]:
                title = item.find('title')
                link = item.find('link')
                description = item.find('description')
                pub_date = item.find('pubDate')
                
                if title is not None and link is not None:
                    job_title = title.text or ""
                    job_url = link.text or ""
                    job_description = description.text if description is not None else ""
                    
                    # Filter by keywords if provided
                    if keywords:
                        title_lower = job_title.lower()
                        desc_lower = job_description.lower()
                        
                        if not any(keyword.lower() in title_lower or keyword.lower() in desc_lower 
                                 for keyword in keywords):
                            continue
                    
                    # Extract company name from title (format: "Company: Job Title")
                    company_name = ""
                    if ": " in job_title:
                        company_name = job_title.split(": ")[0]
                        job_title = job_title.split(": ", 1)[1]
                    
                    job_data = {
                        'id': self._generate_job_id(job_url),
                        'title': job_title,
                        'company': company_name,
                        'url': job_url,
                        'description': job_description,
                        'location': 'Remote',
                        'posted_date': pub_date.text if pub_date is not None else "",
                        'portal': self.portal_name.value
                    }
                    
                    jobs.append(job_data)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to parse We Work Remotely RSS feed: {e}")
            return []
    
    def _generate_job_id(self, job_url: str) -> str:
        """Generate a unique job ID from the URL."""
        # Extract the job ID from the URL
        parsed_url = urlparse(job_url)
        path_parts = parsed_url.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            return f"wwr_{path_parts[-1]}"
        
        # Fallback: use hash of URL
        return f"wwr_{hash(job_url) % 1000000}"


class RemoteCoClient(APIClient):
    """API client for Remote.co."""
    
    def __init__(self):
        super().__init__(JobPortalName.REMOTE_CO, "https://remote.co")
    
    async def _perform_job_search(self, keywords: List[str], location: str = "", 
                                limit: int = 50) -> List[Dict[str, Any]]:
        """Search jobs on Remote.co."""
        try:
            # Remote.co doesn't have a public API, but we can scrape their job listings
            search_url = f"{self.base_url}/remote-jobs/developer/"
            
            response = await self.session.get(search_url)
            response.raise_for_status()
            
            # This would require HTML parsing - placeholder for now
            logger.info("Remote.co job search would require HTML parsing")
            return []
            
        except Exception as e:
            logger.error(f"Failed to search jobs on Remote.co: {e}")
            return []


class JobPortalAPIManager:
    """Manages API clients for different job portals."""
    
    def __init__(self):
        self.clients: Dict[JobPortalName, APIClient] = {}
        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize API clients for supported portals."""
        self.clients[JobPortalName.WE_WORK_REMOTELY] = WeWorkRemotelyClient()
        self.clients[JobPortalName.REMOTE_CO] = RemoteCoClient()
        # Add more clients as they're implemented
    
    async def close_all(self) -> None:
        """Close all API clients."""
        for client in self.clients.values():
            await client.close()
    
    def get_client(self, portal_name: JobPortalName) -> Optional[APIClient]:
        """Get API client for a specific portal."""
        return self.clients.get(portal_name)
    
    async def search_all_portals(self, keywords: List[str], location: str = "", 
                               enabled_portals: List[JobPortalName] = None) -> Dict[JobPortalName, List[Dict[str, Any]]]:
        """Search jobs across all enabled portals."""
        if enabled_portals is None:
            enabled_portals = list(self.clients.keys())
        
        results = {}
        
        for portal_name in enabled_portals:
            client = self.clients.get(portal_name)
            if client:
                try:
                    jobs = await client.search_jobs(keywords, location)
                    results[portal_name] = jobs
                    logger.info(f"Found {len(jobs)} jobs on {portal_name.value}")
                except Exception as e:
                    logger.error(f"Failed to search {portal_name.value}: {e}")
                    results[portal_name] = []
        
        return results
    
    async def get_fresh_jobs(self, hours_back: int = 24) -> Dict[JobPortalName, List[Dict[str, Any]]]:
        """Get jobs posted in the last N hours."""
        cutoff_date = datetime.now() - timedelta(hours=hours_back)
        
        # This would filter jobs by posting date
        # Implementation depends on each portal's date format
        logger.info(f"Getting jobs posted in the last {hours_back} hours")
        
        # For now, return all jobs (would be filtered in a real implementation)
        return await self.search_all_portals([])


# Global API manager instance
api_manager = JobPortalAPIManager()
