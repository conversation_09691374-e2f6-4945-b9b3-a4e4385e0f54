"""
Configuration management for the Job Application Automation System.
Handles environment variables, user settings, and application configuration.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = Field(default="Job Application Automation", env="APP_NAME")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security settings
    master_password: str = Field(default="", env="MASTER_PASSWORD")
    session_timeout: int = Field(default=3600, env="SESSION_TIMEOUT")
    
    # Browser automation settings
    browser_headless: bool = Field(default=True, env="BROWSER_HEADLESS")
    browser_timeout: int = Field(default=30000, env="BROWSER_TIMEOUT")
    automation_delay: int = Field(default=2000, env="AUTOMATION_DELAY")
    
    # Application limits
    daily_application_limit: int = Field(default=10, env="DAILY_APPLICATION_LIMIT")
    max_applications_per_portal: int = Field(default=5, env="MAX_APPLICATIONS_PER_PORTAL")
    
    # Data storage
    data_retention_days: int = Field(default=90, env="DATA_RETENTION_DAYS")
    auto_backup: bool = Field(default=True, env="AUTO_BACKUP")
    
    # Notification settings
    enable_notifications: bool = Field(default=True, env="ENABLE_NOTIFICATIONS")
    email_notifications: bool = Field(default=False, env="EMAIL_NOTIFICATIONS")
    notification_email: str = Field(default="", env="NOTIFICATION_EMAIL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class ConfigManager:
    """Manages application configuration and user settings."""
    
    def __init__(self, config_dir: str = "data"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.settings_file = self.config_dir / "user_settings.json"
        self.credentials_file = self.config_dir / "credentials.json"
        
        self.settings = Settings()
        self._user_settings: Dict[str, Any] = {}
        self._credentials: Dict[str, Any] = {}
        
        self.load_user_settings()
    
    def load_user_settings(self) -> None:
        """Load user settings from file."""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r') as f:
                    self._user_settings = json.load(f)
        except Exception as e:
            print(f"Error loading user settings: {e}")
            self._user_settings = {}
    
    def save_user_settings(self) -> None:
        """Save user settings to file."""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self._user_settings, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving user settings: {e}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a user setting value."""
        return self._user_settings.get(key, default)
    
    def set_setting(self, key: str, value: Any) -> None:
        """Set a user setting value."""
        self._user_settings[key] = value
        self.save_user_settings()
    
    def get_portal_credentials(self, portal_name: str) -> Dict[str, str]:
        """Get credentials for a specific portal."""
        # First check environment variables
        env_email = os.getenv(f"{portal_name.upper()}_EMAIL", "")
        env_password = os.getenv(f"{portal_name.upper()}_PASSWORD", "")
        
        if env_email and env_password:
            return {"email": env_email, "password": env_password}
        
        # Then check stored credentials (encrypted in production)
        return self._credentials.get(portal_name, {})
    
    def set_portal_credentials(self, portal_name: str, email: str, password: str) -> None:
        """Set credentials for a specific portal."""
        # In production, these should be encrypted
        self._credentials[portal_name] = {
            "email": email,
            "password": password
        }
        self._save_credentials()
    
    def _save_credentials(self) -> None:
        """Save credentials to file (should be encrypted in production)."""
        try:
            # TODO: Implement encryption for sensitive data
            with open(self.credentials_file, 'w') as f:
                json.dump(self._credentials, f, indent=2)
        except Exception as e:
            print(f"Error saving credentials: {e}")
    
    def get_data_dir(self) -> Path:
        """Get the data directory path."""
        return self.config_dir
    
    def get_logs_dir(self) -> Path:
        """Get the logs directory path."""
        logs_dir = self.config_dir / "logs"
        logs_dir.mkdir(exist_ok=True)
        return logs_dir
    
    def get_screenshots_dir(self) -> Path:
        """Get the screenshots directory path."""
        screenshots_dir = self.config_dir / "screenshots"
        screenshots_dir.mkdir(exist_ok=True)
        return screenshots_dir
    
    def cleanup_old_data(self) -> None:
        """Clean up old data based on retention policy."""
        retention_days = self.settings.data_retention_days
        # TODO: Implement data cleanup logic
        pass


# Global configuration instance
config_manager = ConfigManager()
settings = config_manager.settings
