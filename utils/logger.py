"""
Logging configuration for the Job Application Automation System.
Provides structured logging with privacy filters and multiple output formats.
"""

import logging
import logging.handlers
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import re

from .config import config_manager


class PrivacyFilter(logging.Filter):
    """Filter to remove sensitive information from logs."""
    
    # Patterns to redact sensitive information
    SENSITIVE_PATTERNS = [
        (re.compile(r'password["\']?\s*[:=]\s*["\']?([^"\'}\s,]+)', re.IGNORECASE), 'password: [REDACTED]'),
        (re.compile(r'email["\']?\s*[:=]\s*["\']?([^"\'}\s,]+)', re.IGNORECASE), 'email: [REDACTED]'),
        (re.compile(r'token["\']?\s*[:=]\s*["\']?([^"\'}\s,]+)', re.IGNORECASE), 'token: [REDACTED]'),
        (re.compile(r'key["\']?\s*[:=]\s*["\']?([^"\'}\s,]+)', re.IGNORECASE), 'key: [REDACTED]'),
        (re.compile(r'secret["\']?\s*[:=]\s*["\']?([^"\'}\s,]+)', re.IGNORECASE), 'secret: [REDACTED]'),
        # Credit card patterns
        (re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'), '[CARD_REDACTED]'),
        # SSN patterns
        (re.compile(r'\b\d{3}-\d{2}-\d{4}\b'), '[SSN_REDACTED]'),
        # Phone numbers
        (re.compile(r'\b\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b'), '[PHONE_REDACTED]'),
    ]
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter log record to remove sensitive information."""
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            message = record.msg
            for pattern, replacement in self.SENSITIVE_PATTERNS:
                message = pattern.sub(replacement, message)
            record.msg = message
        
        # Also filter args if present
        if hasattr(record, 'args') and record.args:
            filtered_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    for pattern, replacement in self.SENSITIVE_PATTERNS:
                        arg = pattern.sub(replacement, arg)
                filtered_args.append(arg)
            record.args = tuple(filtered_args)
        
        return True


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with structured information."""
        # Create base log entry
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        # Format as readable string
        formatted = f"[{log_entry['timestamp']}] {log_entry['level']} - {log_entry['message']}"
        
        if record.exc_info:
            formatted += f"\n{log_entry['exception']}"
        
        return formatted


class LoggerManager:
    """Manages application logging configuration."""
    
    def __init__(self):
        self.logs_dir = config_manager.get_logs_dir()
        self.loggers: Dict[str, logging.Logger] = {}
        self._setup_root_logger()
    
    def _setup_root_logger(self) -> None:
        """Set up the root logger configuration."""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config_manager.settings.log_level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = StructuredFormatter()
        console_handler.setFormatter(console_formatter)
        console_handler.addFilter(PrivacyFilter())
        root_logger.addHandler(console_handler)
        
        # File handler for all logs
        log_file = self.logs_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = StructuredFormatter()
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(PrivacyFilter())
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_file = self.logs_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_file, maxBytes=5*1024*1024, backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        error_handler.addFilter(PrivacyFilter())
        root_logger.addHandler(error_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger with the specified name."""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def log_application_event(self, event_type: str, portal: str, job_id: str, 
                            status: str, details: Optional[Dict[str, Any]] = None) -> None:
        """Log application-specific events."""
        logger = self.get_logger('application_events')
        
        event_data = {
            'event_type': event_type,
            'portal': portal,
            'job_id': job_id,
            'status': status,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            event_data.update(details)
        
        logger.info(f"Application event: {event_type}", extra=event_data)
    
    def log_automation_event(self, action: str, portal: str, success: bool, 
                           duration: Optional[float] = None, error: Optional[str] = None) -> None:
        """Log automation-specific events."""
        logger = self.get_logger('automation_events')
        
        event_data = {
            'action': action,
            'portal': portal,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        
        if duration is not None:
            event_data['duration_seconds'] = duration
        
        if error:
            event_data['error'] = error
        
        level = logging.INFO if success else logging.ERROR
        message = f"Automation {action} {'succeeded' if success else 'failed'} for {portal}"
        
        logger.log(level, message, extra=event_data)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Log security-related events."""
        logger = self.get_logger('security_events')
        
        event_data = {
            'event_type': event_type,
            'timestamp': datetime.now().isoformat()
        }
        event_data.update(details)
        
        logger.warning(f"Security event: {event_type}", extra=event_data)


# Global logger manager instance
logger_manager = LoggerManager()

# Convenience functions
def get_logger(name: str) -> logging.Logger:
    """Get a logger instance."""
    return logger_manager.get_logger(name)

def log_application_event(event_type: str, portal: str, job_id: str, 
                         status: str, details: Optional[Dict[str, Any]] = None) -> None:
    """Log application event."""
    logger_manager.log_application_event(event_type, portal, job_id, status, details)

def log_automation_event(action: str, portal: str, success: bool, 
                        duration: Optional[float] = None, error: Optional[str] = None) -> None:
    """Log automation event."""
    logger_manager.log_automation_event(action, portal, success, duration, error)

def log_security_event(event_type: str, details: Dict[str, Any]) -> None:
    """Log security event."""
    logger_manager.log_security_event(event_type, details)
